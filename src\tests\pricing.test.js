/**
 * Basic tests for pricing functionality
 * These tests verify that our pricing data and utilities work correctly
 */

import { 
  getPricingPlans, 
  getAvailableDurations, 
  getPlansByDuration, 
  formatPrice, 
  formatPlanFeatures,
  calculateSavings 
} from '../data/pricing-plans.js';

// Test data loading
console.log('🧪 Testing Pricing Data Loading...');

try {
  const plans = getPricingPlans();
  console.log('✅ Plans loaded successfully:', plans.length, 'plans found');
  
  if (plans.length >= 4 && plans.length <= 5) {
    console.log('✅ Correct number of plans (4-5)');
  } else {
    console.log('❌ Incorrect number of plans:', plans.length);
  }
} catch (error) {
  console.log('❌ Error loading plans:', error.message);
}

// Test duration extraction
console.log('\n🧪 Testing Duration Extraction...');

try {
  const durations = getAvailableDurations();
  console.log('✅ Durations extracted:', durations.map(d => d.label).join(', '));
  
  const expectedDurations = ['1 Month', '3 Months', '6 Months', '1 Year'];
  const hasAllDurations = expectedDurations.every(expected => 
    durations.some(d => d.label === expected)
  );
  
  if (hasAllDurations) {
    console.log('✅ All expected durations found');
  } else {
    console.log('❌ Missing some expected durations');
  }
} catch (error) {
  console.log('❌ Error extracting durations:', error.message);
}

// Test plan filtering by duration
console.log('\n🧪 Testing Plan Filtering by Duration...');

try {
  const monthlyPlans = getPlansByDuration('1-month');
  console.log('✅ Monthly plans found:', monthlyPlans.length);
  
  monthlyPlans.forEach(plan => {
    console.log(`  - ${plan.name}: ${formatPrice(plan.price, plan.currency)}`);
  });
  
  if (monthlyPlans.length > 0) {
    console.log('✅ Plan filtering works correctly');
  } else {
    console.log('❌ No monthly plans found');
  }
} catch (error) {
  console.log('❌ Error filtering plans:', error.message);
}

// Test price formatting
console.log('\n🧪 Testing Price Formatting...');

try {
  const testPrices = [1500, 3500, 15000, 150000];
  testPrices.forEach(price => {
    const formatted = formatPrice(price, 'INR');
    console.log(`✅ ${price} → ${formatted}`);
  });
} catch (error) {
  console.log('❌ Error formatting prices:', error.message);
}

// Test feature formatting
console.log('\n🧪 Testing Feature Formatting...');

try {
  const sampleFeatures = [
    {
      id: '1',
      name: 'Diet Consultation',
      description: 'Personalized diet consulting',
      isActive: true,
      customSort: 0
    },
    {
      id: '2',
      name: 'Inactive Feature',
      description: 'This should be filtered out',
      isActive: false,
      customSort: 1
    }
  ];
  
  const formatted = formatPlanFeatures(sampleFeatures);
  console.log('✅ Features formatted:', formatted.length, 'active features');
  
  if (formatted.length === 1 && formatted[0].name === 'Diet Consultation') {
    console.log('✅ Feature filtering works correctly');
  } else {
    console.log('❌ Feature filtering failed');
  }
} catch (error) {
  console.log('❌ Error formatting features:', error.message);
}

// Test savings calculation
console.log('\n🧪 Testing Savings Calculation...');

try {
  const monthlyPrice = 2000;
  const threeMonthPrice = 5500;
  const savings = calculateSavings(threeMonthPrice, monthlyPrice, 3);
  
  console.log(`✅ Savings calculation: ${savings}% saved on 3-month plan`);
  
  if (savings > 0 && savings < 100) {
    console.log('✅ Savings calculation works correctly');
  } else {
    console.log('❌ Savings calculation seems incorrect');
  }
} catch (error) {
  console.log('❌ Error calculating savings:', error.message);
}

console.log('\n🎉 Pricing tests completed!');

// Export for potential use in other test files
export {
  getPricingPlans,
  getAvailableDurations,
  getPlansByDuration,
  formatPrice,
  formatPlanFeatures,
  calculateSavings
};
