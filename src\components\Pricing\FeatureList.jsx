"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { CheckIcon } from "@heroicons/react/24/solid";

const FeatureList = ({
  features = [],
  maxVisible = null,
  showDescriptions = false,
  className = "",
  variant = "default",
  onShowMore = null,
}) => {
  const [showAll, setShowAll] = useState(false);
  const effectiveMaxVisible = showAll ? null : maxVisible;
  const displayFeatures = effectiveMaxVisible
    ? features.slice(0, effectiveMaxVisible)
    : features;
  const remainingCount =
    effectiveMaxVisible && features.length > effectiveMaxVisible
      ? features.length - effectiveMaxVisible
      : 0;

  const variants = {
    default: {
      container: "space-y-3",
      item: "flex items-start space-x-3",
      icon: "w-5 h-5 text-teal-500 mt-0.5 flex-shrink-0",
      text: "text-gray-700",
      description: "text-sm text-gray-500 mt-1",
    },
    compact: {
      container: "space-y-2",
      item: "flex items-center space-x-2",
      icon: "w-4 h-4 text-teal-500 flex-shrink-0",
      text: "text-sm text-gray-700",
      description: "text-xs text-gray-500 mt-0.5",
    },
    detailed: {
      container: "space-y-4",
      item: "flex items-start space-x-4 p-3 bg-gray-50 rounded-lg",
      icon: "w-6 h-6 text-teal-500 mt-1 flex-shrink-0",
      text: "text-gray-800 font-medium",
      description: "text-sm text-gray-600 mt-2",
    },
  };

  const currentVariant = variants[variant];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        type: "spring",
        stiffness: 500,
        damping: 30,
      },
    },
  };

  if (!features || features.length === 0) {
    return (
      <div className={`text-center py-4 ${className}`}>
        <p className="text-gray-500 text-sm">No features available</p>
      </div>
    );
  }

  return (
    <motion.div
      className={`${currentVariant.container} ${className}`}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {displayFeatures.map((feature, index) => (
        <motion.div
          key={feature.id || index}
          className={currentVariant.item}
          variants={itemVariants}
        >
          <div className="flex-shrink-0">
            <motion.div
              className={`
                rounded-full bg-teal-100 p-1
                ${currentVariant.icon.includes("w-4") ? "p-0.5" : "p-1"}
                ${currentVariant.icon.includes("w-6") ? "p-1.5" : ""}
              `}
              whileHover={{ scale: 1.1 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <CheckIcon className={currentVariant.icon} />
            </motion.div>
          </div>

          <div className="flex-1 min-w-0">
            <p className={currentVariant.text}>{feature.name}</p>
            {showDescriptions && feature.description && (
              <p className={currentVariant.description}>
                {feature.description}
              </p>
            )}
          </div>
        </motion.div>
      ))}

      {remainingCount > 0 && (
        <motion.button
          className="flex items-center space-x-3 pt-2 w-full text-left hover:bg-gray-50 rounded-lg p-2 transition-colors duration-200"
          variants={itemVariants}
          onClick={() => {
            setShowAll(true);
            if (onShowMore) onShowMore();
          }}
          whileHover={{ x: 4 }}
        >
          <div className="flex-shrink-0">
            <div className="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center">
              <span className="text-xs text-gray-500 font-medium">+</span>
            </div>
          </div>
          <p className="text-sm text-gray-500 font-medium hover:text-gray-700">
            {remainingCount} more feature{remainingCount > 1 ? "s" : ""} - Click
            to show all
          </p>
        </motion.button>
      )}

      {showAll && maxVisible && (
        <motion.button
          className="flex items-center space-x-3 pt-2 w-full text-left hover:bg-gray-50 rounded-lg p-2 transition-colors duration-200"
          onClick={() => setShowAll(false)}
          whileHover={{ x: 4 }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          <div className="flex-shrink-0">
            <div className="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center">
              <span className="text-xs text-gray-500 font-medium">−</span>
            </div>
          </div>
          <p className="text-sm text-gray-500 font-medium hover:text-gray-700">
            Show less
          </p>
        </motion.button>
      )}
    </motion.div>
  );
};

export default FeatureList;
