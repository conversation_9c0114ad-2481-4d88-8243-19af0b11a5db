"use client";

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDownIcon } from '@heroicons/react/24/outline';

const DurationSelector = ({ 
  durations = [], 
  selectedDuration, 
  onDurationChange,
  className = '',
  size = 'md'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  
  const sizes = {
    sm: {
      button: 'px-3 py-2 text-sm',
      dropdown: 'py-1',
      option: 'px-3 py-2 text-sm'
    },
    md: {
      button: 'px-4 py-2.5 text-base',
      dropdown: 'py-2',
      option: 'px-4 py-3 text-base'
    },
    lg: {
      button: 'px-6 py-3 text-lg',
      dropdown: 'py-3',
      option: 'px-6 py-4 text-lg'
    }
  };
  
  const currentSize = sizes[size];
  
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);
  
  // Close dropdown on escape key
  useEffect(() => {
    const handleEscape = (event) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };
    
    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, []);
  
  const handleDurationSelect = (duration) => {
    onDurationChange(duration);
    setIsOpen(false);
  };
  
  const selectedDurationData = durations.find(d => d.slug === selectedDuration) || durations[0];
  
  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Trigger Button */}
      <motion.button
        type="button"
        className={`
          w-full flex items-center justify-between
          bg-white border border-gray-300 rounded-lg
          hover:border-teal-500 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500
          transition-colors duration-200
          ${currentSize.button}
        `}
        onClick={() => setIsOpen(!isOpen)}
        whileTap={{ scale: 0.98 }}
      >
        <span className="font-medium text-gray-900">
          {selectedDurationData?.label || 'Select Duration'}
        </span>
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.2 }}
        >
          <ChevronDownIcon className="h-5 w-5 text-gray-400" />
        </motion.div>
      </motion.button>
      
      {/* Dropdown Menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className={`
              absolute z-50 w-full mt-2
              bg-white border border-gray-200 rounded-lg shadow-xl
              ${currentSize.dropdown}
            `}
          >
            <div className="max-h-60 overflow-auto">
              {durations.map((duration, index) => (
                <motion.button
                  key={duration.slug}
                  type="button"
                  className={`
                    w-full text-left flex items-center justify-between
                    hover:bg-teal-50 hover:text-teal-700
                    transition-colors duration-150
                    ${currentSize.option}
                    ${selectedDuration === duration.slug ? 'bg-teal-50 text-teal-700 font-medium' : 'text-gray-900'}
                    ${index === 0 ? 'rounded-t-lg' : ''}
                    ${index === durations.length - 1 ? 'rounded-b-lg' : ''}
                  `}
                  onClick={() => handleDurationSelect(duration)}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  whileHover={{ x: 4 }}
                >
                  <div>
                    <div className="font-medium">{duration.label}</div>
                    <div className="text-xs text-gray-500 mt-0.5">
                      {duration.valueInDays} days
                    </div>
                  </div>
                  {selectedDuration === duration.slug && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="w-2 h-2 bg-teal-500 rounded-full"
                    />
                  )}
                </motion.button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default DurationSelector;
