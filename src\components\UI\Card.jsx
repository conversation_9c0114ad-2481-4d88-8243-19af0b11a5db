"use client";

import { motion } from 'framer-motion';

const Card = ({ 
  children, 
  variant = 'default',
  hover = true,
  className = '',
  ...props 
}) => {
  const baseClasses = 'bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden';
  
  const variants = {
    default: '',
    popular: 'ring-2 ring-teal-500 ring-opacity-50 shadow-2xl',
    featured: 'bg-gradient-to-br from-teal-50 to-blue-50 border-teal-200'
  };
  
  const classes = `${baseClasses} ${variants[variant]} ${className}`;
  
  const hoverAnimation = hover ? {
    whileHover: { 
      y: -4,
      boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
    },
    transition: { type: "spring", stiffness: 300, damping: 30 }
  } : {};
  
  return (
    <motion.div
      className={classes}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      {...hoverAnimation}
      {...props}
    >
      {children}
    </motion.div>
  );
};

export default Card;
