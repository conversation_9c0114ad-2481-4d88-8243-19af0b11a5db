"use client";

import { motion } from "framer-motion";
import Card from "../UI/Card";
import Button from "../UI/Button";
import Tag from "../UI/Tag";
import DurationSelector from "./DurationSelector";
import FeatureList from "./FeatureList";

// Import utility functions from API helper
import {
  formatPrice,
  formatPlanFeatures,
  calculateSavings,
} from "../../utils/api";

const PlanCard = ({
  plan,
  availableDurations = [],
  selectedDuration,
  onDurationChange,
  isPopular = false,
  className = "",
  onBuyNow,
}) => {
  if (!plan) {
    return null;
  }

  // Find the monthly plan for savings calculation
  const monthlyDuration = availableDurations.find((d) => d.valueInDays === 30);
  const currentDurationMonths = Math.round(plan.duration.valueInDays / 30);

  // Calculate savings if applicable
  const savings =
    monthlyDuration && currentDurationMonths > 1
      ? calculateSavings(
          plan.price,
          monthlyDuration.price,
          currentDurationMonths
        )
      : 0;

  const formattedFeatures = formatPlanFeatures(plan.features);
  const maxVisibleFeatures = 6; // Show first 6 features, then "+X more"

  return (
    <motion.div
      className={`relative ${className}`}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      layout
    >
      {/* Popular Badge */}
      {isPopular && (
        <motion.div
          className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Tag variant="popular" size="md">
            Most Popular
          </Tag>
        </motion.div>
      )}

      {/* Savings Badge */}
      {savings > 0 && (
        <motion.div
          className="absolute -top-3 right-4 z-10"
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.3, type: "spring", stiffness: 500 }}
        >
          <Tag variant="savings" size="sm">
            Save {savings}%
          </Tag>
        </motion.div>
      )}

      <Card
        variant={isPopular ? "popular" : "default"}
        hover={true}
        className="h-full"
      >
        {/* Header */}
        <div className="p-4 sm:p-6 pb-4">
          <div className="text-center mb-4 sm:mb-6">
            <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2">
              {plan.name}
            </h3>
            {plan.description && (
              <p className="text-gray-600 text-xs sm:text-sm leading-relaxed px-2">
                {plan.description}
              </p>
            )}
          </div>

          {/* Duration Selector */}
          <div className="mb-4 sm:mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Duration
            </label>
            <DurationSelector
              durations={availableDurations}
              selectedDuration={selectedDuration}
              onDurationChange={onDurationChange}
              size="md"
            />
          </div>

          {/* Price Display */}
          <motion.div
            className="text-center mb-4 sm:mb-6"
            key={plan.price} // Re-animate when price changes
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex items-baseline justify-center space-x-1 sm:space-x-2">
              <span className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900">
                {formatPrice(plan.price, plan.currency)}
              </span>
              <span className="text-gray-500 text-sm sm:text-base lg:text-lg">
                / {plan.duration.label.toLowerCase()}
              </span>
            </div>

            {currentDurationMonths > 1 && (
              <div className="mt-1 sm:mt-2 text-xs sm:text-sm text-gray-500">
                {formatPrice(
                  Math.round(plan.price / currentDurationMonths),
                  plan.currency
                )}{" "}
                per month
              </div>
            )}

            <div className="mt-1 text-xs text-gray-400 uppercase tracking-wide">
              {plan.paymentType} payment
            </div>
          </motion.div>
        </div>

        {/* Features */}
        <div className="px-4 sm:px-6 pb-4 sm:pb-6">
          <h4 className="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4">
            What's Included
          </h4>
          <FeatureList
            features={formattedFeatures}
            maxVisible={maxVisibleFeatures}
            showDescriptions={false}
            variant="compact"
          />
        </div>

        {/* Footer with Buy Now Button */}
        <div className="p-4 sm:p-6 pt-0 mt-auto">
          <Button
            variant="primary"
            size="lg"
            className="w-full text-sm sm:text-base"
            onClick={() => onBuyNow && onBuyNow(plan)}
          >
            Buy Now
          </Button>

          <p className="text-xs text-gray-500 text-center mt-2 sm:mt-3 leading-relaxed">
            No hidden fees • Cancel anytime • 30-day money back guarantee
          </p>
        </div>
      </Card>
    </motion.div>
  );
};

export default PlanCard;
