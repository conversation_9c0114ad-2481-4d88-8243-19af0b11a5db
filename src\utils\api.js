/**
 * Centralized API helper for all API calls
 * Provides caching, error handling, and consistent API interface
 */

// API Configuration
const API_CONFIG = {
  BASE_URL: "http://localhost:8000",
  ENDPOINTS: {
    PRICING_PLANS: "/api/pricing-plans",
    // Add other endpoints here as needed
  },
  CACHE_DURATION: 5 * 60 * 1000, // 5 minutes
};

// Cache storage
const cache = new Map();

/**
 * Generic API call function with caching and error handling
 * @param {string} endpoint - API endpoint
 * @param {Object} options - Fetch options
 * @returns {Promise<Object>} API response
 */
async function apiCall(endpoint, options = {}) {
  const url = `${API_CONFIG.BASE_URL}${endpoint}`;
  const cacheKey = `${url}_${JSON.stringify(options)}`;

  // Check cache first
  const cachedData = cache.get(cacheKey);
  if (
    cachedData &&
    Date.now() - cachedData.timestamp < API_CONFIG.CACHE_DURATION
  ) {
    return cachedData.data;
  }

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Cache the response
    cache.set(cacheKey, {
      data,
      timestamp: Date.now(),
    });

    return data;
  } catch (error) {
    console.error(`API call failed for ${endpoint}:`, error);
    throw error;
  }
}

/**
 * Clear cache for specific endpoint or all cache
 * @param {string} endpoint - Optional endpoint to clear, if not provided clears all
 */
export function clearCache(endpoint = null) {
  if (endpoint) {
    const url = `${API_CONFIG.BASE_URL}${endpoint}`;
    for (const [key] of cache) {
      if (key.startsWith(url)) {
        cache.delete(key);
      }
    }
  } else {
    cache.clear();
  }
}

/**
 * Fetch pricing plans from API
 * @returns {Promise<Object>} Raw API response
 */
export async function fetchPricingPlans() {
  return await apiCall(API_CONFIG.ENDPOINTS.PRICING_PLANS);
}

/**
 * Get processed pricing plans data
 * @returns {Promise<Array>} Array of pricing plans
 */
export async function getPricingPlans() {
  try {
    const response = await fetchPricingPlans();

    if (!response || !response.data || !Array.isArray(response.data)) {
      throw new Error("Invalid pricing plans data structure");
    }

    return response.data;
  } catch (error) {
    console.error("Error processing pricing plans:", error);
    return [];
  }
}

/**
 * Get all available durations across all plans
 * @returns {Promise<Array>} Array of duration objects
 */
export async function getAvailableDurations() {
  try {
    const plans = await getPricingPlans();
    const durationsMap = new Map();

    plans.forEach((plan) => {
      if (plan.durations && Array.isArray(plan.durations)) {
        plan.durations.forEach((duration) => {
          if (!durationsMap.has(duration.slug)) {
            durationsMap.set(duration.slug, {
              id: duration.id,
              label: duration.label,
              valueInDays: duration.valueInDays,
              slug: duration.slug,
            });
          }
        });
      }
    });

    return Array.from(durationsMap.values()).sort(
      (a, b) => a.valueInDays - b.valueInDays
    );
  } catch (error) {
    console.error("Error getting available durations:", error);
    return [];
  }
}

/**
 * Process plans data for a specific duration
 * @param {Array} plans - Raw plans data
 * @param {string} durationSlug - Duration slug to filter by
 * @returns {Array} Processed plans for the duration
 */
export function processPlansByDuration(plans, durationSlug) {
  return plans
    .map((plan) => {
      const duration = plan.durations?.find((d) => d.slug === durationSlug);

      if (!duration) {
        return null;
      }

      return {
        id: plan.id,
        name: plan.name,
        slug: plan.slug,
        description: plan.description,
        isPopular: plan.isPopular || false,
        duration: {
          id: duration.id,
          label: duration.label,
          valueInDays: duration.valueInDays,
          slug: duration.slug,
        },
        price: duration.price,
        currency: duration.currency || "INR",
        paymentType: duration.paymentType || "one-time",
        features: duration.features || [],
      };
    })
    .filter(Boolean);
}

/**
 * Get plan data for a specific duration (using cached data)
 * @param {Array} plans - Cached plans data
 * @param {string} durationSlug - Duration slug
 * @returns {Array} Plans for the specified duration
 */
export function getPlansByDuration(plans, durationSlug) {
  return processPlansByDuration(plans, durationSlug);
}

/**
 * Format price for display
 * @param {number} price - Price amount
 * @param {string} currency - Currency code
 * @returns {string} Formatted price string
 */
export function formatPrice(price, currency = "INR") {
  if (typeof price !== "number") {
    return "Price not available";
  }

  const currencySymbols = {
    INR: "₹",
    USD: "$",
    EUR: "€",
    GBP: "£",
  };

  const symbol = currencySymbols[currency] || currency;

  if (currency === "INR") {
    return `${symbol} ${price.toLocaleString("en-IN")}`;
  }

  return `${symbol} ${price.toLocaleString()}`;
}

/**
 * Format plan features for display
 * @param {Array} features - Array of feature objects
 * @returns {Array} Formatted features array
 */
export function formatPlanFeatures(features) {
  if (!Array.isArray(features)) {
    return [];
  }

  return features
    .filter((feature) => feature.isActive)
    .sort((a, b) => (a.customSort || 0) - (b.customSort || 0))
    .map((feature) => ({
      id: feature.id,
      name: feature.name,
      description: feature.description,
      slug: feature.slug,
    }));
}

/**
 * Calculate savings percentage
 * @param {number} currentPrice - Current plan price
 * @param {number} monthlyPrice - Monthly plan price
 * @param {number} durationInMonths - Duration in months
 * @returns {number} Savings percentage
 */
export function calculateSavings(currentPrice, monthlyPrice, durationInMonths) {
  if (!monthlyPrice || !durationInMonths || durationInMonths <= 1) {
    return 0;
  }

  const totalMonthlyPrice = monthlyPrice * durationInMonths;
  const savings = totalMonthlyPrice - currentPrice;
  const savingsPercentage = (savings / totalMonthlyPrice) * 100;

  return Math.max(0, Math.round(savingsPercentage));
}
