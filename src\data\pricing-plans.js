// Mock pricing data based on the actual API structure
// This simulates the data from pricing-plans.js in the root directory
const MOCK_PRICING_DATA = {
  status: "Success",
  message: "Pricing plans retrieved successfully",
  data: [
    {
      id: "68372a398cd0aff33c968ed9",
      name: "Workout Plan",
      slug: "workout-plan",
      description:
        "Custom workout programming designed for strength, endurance, and mobility.",
      durations: [
        {
          id: "683d1e287efe77074e8cd057",
          label: "1 Month",
          valueInDays: 30,
          slug: "1-month",
          features: [
            {
              id: "683725788cd0aff33c968e80",
              name: "Diet Consultation",
              description:
                "Personalized diet consulting tailored to user goals.",
              slug: "diet-consultation",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725838cd0aff33c968e84",
              name: "Workout Consultation",
              description:
                "One-on-one workout consultation, goal-specific planning.",
              slug: "workout-consultation",
              isActive: true,
              customSort: 0,
            },
            {
              id: "6837259a8cd0aff33c968e88",
              name: "Step Challenge",
              description:
                "Customizable step count challenge – competitive or solo.",
              slug: "step-challenge",
              isActive: true,
              customSort: 0,
            },
          ],
          price: 2000,
          currency: "INR",
          paymentType: "one-time",
          isActive: true,
        },
        {
          id: "6837281c8cd0aff33c968ec3",
          label: "3 Months",
          valueInDays: 90,
          slug: "3-months",
          features: [
            {
              id: "683725788cd0aff33c968e80",
              name: "Diet Consultation",
              description:
                "Personalized diet consulting tailored to user goals.",
              slug: "diet-consultation",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725cd8cd0aff33c968e94",
              name: "Diet/Workout Plan Update",
              description:
                "Revisions to existing plans based on user progress.",
              slug: "diet/workout-plan-update",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725df8cd0aff33c968e98",
              name: "Wellness Trackers",
              description: "Daily tracking of steps, sleep, and water intake.",
              slug: "wellness-trackers",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725ec8cd0aff33c968e9c",
              name: "Monthly Check-in Calls",
              description:
                "4–6 calls/month (minimum 3 minutes each) based on plan.",
              slug: "monthly-check-in-calls",
              isActive: true,
              customSort: 0,
            },
          ],
          price: 5500,
          currency: "INR",
          paymentType: "one-time",
          isActive: true,
        },
        {
          id: "683728358cd0aff33c968ec7",
          label: "6 Months",
          valueInDays: 180,
          slug: "6-months",
          features: [
            {
              id: "683725788cd0aff33c968e80",
              name: "Diet Consultation",
              description:
                "Personalized diet consulting tailored to user goals.",
              slug: "diet-consultation",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725838cd0aff33c968e84",
              name: "Workout Consultation",
              description:
                "One-on-one workout consultation, goal-specific planning.",
              slug: "workout-consultation",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725cd8cd0aff33c968e94",
              name: "Diet/Workout Plan Update",
              description:
                "Revisions to existing plans based on user progress.",
              slug: "diet/workout-plan-update",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725df8cd0aff33c968e98",
              name: "Wellness Trackers",
              description: "Daily tracking of steps, sleep, and water intake.",
              slug: "wellness-trackers",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725ec8cd0aff33c968e9c",
              name: "Monthly Check-in Calls",
              description:
                "4–6 calls/month (minimum 3 minutes each) based on plan.",
              slug: "monthly-check-in-calls",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725fd8cd0aff33c968ea0",
              name: "Chat Support",
              description:
                "Ongoing chat support for quick queries and guidance.",
              slug: "chat-support",
              isActive: true,
              customSort: 0,
            },
          ],
          price: 9000,
          currency: "INR",
          paymentType: "one-time",
          isActive: true,
        },
        {
          id: "683728538cd0aff33c968ecb",
          label: "1 Year",
          valueInDays: 365,
          slug: "1-year",
          features: [
            {
              id: "683725788cd0aff33c968e80",
              name: "Diet Consultation",
              description:
                "Personalized diet consulting tailored to user goals.",
              slug: "diet-consultation",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725838cd0aff33c968e84",
              name: "Workout Consultation",
              description:
                "One-on-one workout consultation, goal-specific planning.",
              slug: "workout-consultation",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725cd8cd0aff33c968e94",
              name: "Diet/Workout Plan Update",
              description:
                "Revisions to existing plans based on user progress.",
              slug: "diet/workout-plan-update",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725df8cd0aff33c968e98",
              name: "Wellness Trackers",
              description: "Daily tracking of steps, sleep, and water intake.",
              slug: "wellness-trackers",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725ec8cd0aff33c968e9c",
              name: "Monthly Check-in Calls",
              description:
                "4–6 calls/month (minimum 3 minutes each) based on plan.",
              slug: "monthly-check-in-calls",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725fd8cd0aff33c968ea0",
              name: "Chat Support",
              description:
                "Ongoing chat support for quick queries and guidance.",
              slug: "chat-support",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683726138cd0aff33c968ea4",
              name: "Custom Education Request",
              description:
                "On-demand education modules on topics of your choice.",
              slug: "custom-education-request",
              isActive: true,
              customSort: 1,
            },
          ],
          price: 15000,
          currency: "INR",
          paymentType: "one-time",
          isActive: true,
        },
      ],
      isActive: true,
      isPopular: false,
    },
    {
      id: "68372a578cd0aff33c968edc",
      name: "Diet + Workout Plan",
      slug: "diet-workout-plan",
      description:
        "Comprehensive plan combining both personalized diet and workout guidance.",
      durations: [
        {
          id: "683d1e287efe77074e8cd057",
          label: "1 Month",
          valueInDays: 30,
          slug: "1-month",
          features: [
            {
              id: "683725788cd0aff33c968e80",
              name: "Diet Consultation",
              description:
                "Personalized diet consulting tailored to user goals.",
              slug: "diet-consultation",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725838cd0aff33c968e84",
              name: "Workout Consultation",
              description:
                "One-on-one workout consultation, goal-specific planning.",
              slug: "workout-consultation",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725cd8cd0aff33c968e94",
              name: "Diet/Workout Plan Update",
              description:
                "Revisions to existing plans based on user progress.",
              slug: "diet/workout-plan-update",
              isActive: true,
              customSort: 0,
            },
            {
              id: "6837259a8cd0aff33c968e88",
              name: "Step Challenge",
              description:
                "Customizable step count challenge – competitive or solo.",
              slug: "step-challenge",
              isActive: true,
              customSort: 0,
            },
          ],
          price: 3500,
          currency: "INR",
          paymentType: "one-time",
          isActive: true,
        },
        {
          id: "6837281c8cd0aff33c968ec3",
          label: "3 Months",
          valueInDays: 90,
          slug: "3-months",
          features: [
            {
              id: "683725788cd0aff33c968e80",
              name: "Diet Consultation",
              description:
                "Personalized diet consulting tailored to user goals.",
              slug: "diet-consultation",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725838cd0aff33c968e84",
              name: "Workout Consultation",
              description:
                "One-on-one workout consultation, goal-specific planning.",
              slug: "workout-consultation",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725cd8cd0aff33c968e94",
              name: "Diet/Workout Plan Update",
              description:
                "Revisions to existing plans based on user progress.",
              slug: "diet/workout-plan-update",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725df8cd0aff33c968e98",
              name: "Wellness Trackers",
              description: "Daily tracking of steps, sleep, and water intake.",
              slug: "wellness-trackers",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725ec8cd0aff33c968e9c",
              name: "Monthly Check-in Calls",
              description:
                "4–6 calls/month (minimum 3 minutes each) based on plan.",
              slug: "monthly-check-in-calls",
              isActive: true,
              customSort: 0,
            },
          ],
          price: 10000,
          currency: "INR",
          paymentType: "one-time",
          isActive: true,
        },
        {
          id: "683728358cd0aff33c968ec7",
          label: "6 Months",
          valueInDays: 180,
          slug: "6-months",
          features: [
            {
              id: "683725788cd0aff33c968e80",
              name: "Diet Consultation",
              description:
                "Personalized diet consulting tailored to user goals.",
              slug: "diet-consultation",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725838cd0aff33c968e84",
              name: "Workout Consultation",
              description:
                "One-on-one workout consultation, goal-specific planning.",
              slug: "workout-consultation",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725cd8cd0aff33c968e94",
              name: "Diet/Workout Plan Update",
              description:
                "Revisions to existing plans based on user progress.",
              slug: "diet/workout-plan-update",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725df8cd0aff33c968e98",
              name: "Wellness Trackers",
              description: "Daily tracking of steps, sleep, and water intake.",
              slug: "wellness-trackers",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725ec8cd0aff33c968e9c",
              name: "Monthly Check-in Calls",
              description:
                "4–6 calls/month (minimum 3 minutes each) based on plan.",
              slug: "monthly-check-in-calls",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725fd8cd0aff33c968ea0",
              name: "Chat Support",
              description:
                "Ongoing chat support for quick queries and guidance.",
              slug: "chat-support",
              isActive: true,
              customSort: 0,
            },
          ],
          price: 18000,
          currency: "INR",
          paymentType: "one-time",
          isActive: true,
        },
        {
          id: "683728538cd0aff33c968ecb",
          label: "1 Year",
          valueInDays: 365,
          slug: "1-year",
          features: [
            {
              id: "683725788cd0aff33c968e80",
              name: "Diet Consultation",
              description:
                "Personalized diet consulting tailored to user goals.",
              slug: "diet-consultation",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725838cd0aff33c968e84",
              name: "Workout Consultation",
              description:
                "One-on-one workout consultation, goal-specific planning.",
              slug: "workout-consultation",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725cd8cd0aff33c968e94",
              name: "Diet/Workout Plan Update",
              description:
                "Revisions to existing plans based on user progress.",
              slug: "diet/workout-plan-update",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725df8cd0aff33c968e98",
              name: "Wellness Trackers",
              description: "Daily tracking of steps, sleep, and water intake.",
              slug: "wellness-trackers",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725ec8cd0aff33c968e9c",
              name: "Monthly Check-in Calls",
              description:
                "4–6 calls/month (minimum 3 minutes each) based on plan.",
              slug: "monthly-check-in-calls",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725fd8cd0aff33c968ea0",
              name: "Chat Support",
              description:
                "Ongoing chat support for quick queries and guidance.",
              slug: "chat-support",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683726138cd0aff33c968ea4",
              name: "Custom Education Request",
              description:
                "On-demand education modules on topics of your choice.",
              slug: "custom-education-request",
              isActive: true,
              customSort: 1,
            },
          ],
          price: 30000,
          currency: "INR",
          paymentType: "one-time",
          isActive: true,
        },
      ],
      isActive: true,
      isPopular: true,
    },
    {
      id: "68372a6a8cd0aff33c968edf",
      name: "Premium Plan",
      slug: "premium-plan",
      description:
        "Includes everything from the basic plans along with priority support, educational content, video sessions, and access to exclusive tools.",
      durations: [
        {
          id: "683d1e287efe77074e8cd057",
          label: "1 Month",
          valueInDays: 30,
          slug: "1-month",
          features: [
            {
              id: "683725788cd0aff33c968e80",
              name: "Diet Consultation",
              description:
                "Personalized diet consulting tailored to user goals.",
              slug: "diet-consultation",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725838cd0aff33c968e84",
              name: "Workout Consultation",
              description:
                "One-on-one workout consultation, goal-specific planning.",
              slug: "workout-consultation",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683726558cd0aff33c968eb4",
              name: "45-min Video Call Training",
              description: "Personalized video training session with a coach.",
              slug: "45-min-video-call-training",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725fd8cd0aff33c968ea0",
              name: "Chat Support",
              description:
                "Ongoing chat support for quick queries and guidance.",
              slug: "chat-support",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683726138cd0aff33c968ea4",
              name: "Custom Education Request",
              description:
                "On-demand education modules on topics of your choice.",
              slug: "custom-education-request",
              isActive: true,
              customSort: 1,
            },
          ],
          price: 15000,
          currency: "INR",
          paymentType: "one-time",
          isActive: true,
        },
        {
          id: "6837281c8cd0aff33c968ec3",
          label: "3 Months",
          valueInDays: 90,
          slug: "3-months",
          features: [
            {
              id: "683725788cd0aff33c968e80",
              name: "Diet Consultation",
              description:
                "Personalized diet consulting tailored to user goals.",
              slug: "diet-consultation",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725838cd0aff33c968e84",
              name: "Workout Consultation",
              description:
                "One-on-one workout consultation, goal-specific planning.",
              slug: "workout-consultation",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683726558cd0aff33c968eb4",
              name: "45-min Video Call Training",
              description: "Personalized video training session with a coach.",
              slug: "45-min-video-call-training",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725fd8cd0aff33c968ea0",
              name: "Chat Support",
              description:
                "Ongoing chat support for quick queries and guidance.",
              slug: "chat-support",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683726138cd0aff33c968ea4",
              name: "Custom Education Request",
              description:
                "On-demand education modules on topics of your choice.",
              slug: "custom-education-request",
              isActive: true,
              customSort: 1,
            },
            {
              id: "683726228cd0aff33c968ea8",
              name: "Basics of Diet",
              description:
                "Introduction to nutrition principles and macronutrients.",
              slug: "basics-of-diet",
              isActive: true,
              customSort: 0,
            },
          ],
          price: 42000,
          currency: "INR",
          paymentType: "one-time",
          isActive: true,
        },
        {
          id: "683728358cd0aff33c968ec7",
          label: "6 Months",
          valueInDays: 180,
          slug: "6-months",
          features: [
            {
              id: "683725788cd0aff33c968e80",
              name: "Diet Consultation",
              description:
                "Personalized diet consulting tailored to user goals.",
              slug: "diet-consultation",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725838cd0aff33c968e84",
              name: "Workout Consultation",
              description:
                "One-on-one workout consultation, goal-specific planning.",
              slug: "workout-consultation",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683726558cd0aff33c968eb4",
              name: "45-min Video Call Training",
              description: "Personalized video training session with a coach.",
              slug: "45-min-video-call-training",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725fd8cd0aff33c968ea0",
              name: "Chat Support",
              description:
                "Ongoing chat support for quick queries and guidance.",
              slug: "chat-support",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683726138cd0aff33c968ea4",
              name: "Custom Education Request",
              description:
                "On-demand education modules on topics of your choice.",
              slug: "custom-education-request",
              isActive: true,
              customSort: 1,
            },
            {
              id: "683726228cd0aff33c968ea8",
              name: "Basics of Diet",
              description:
                "Introduction to nutrition principles and macronutrients.",
              slug: "basics-of-diet",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683726338cd0aff33c968eac",
              name: "Basics of Muscles & Fitness Hacks",
              description:
                "Learn muscle groups, function, and performance tips.",
              slug: "basics-of-muscles-&-fitness-hacks",
              isActive: true,
              customSort: 0,
            },
          ],
          price: 80000,
          currency: "INR",
          paymentType: "one-time",
          isActive: true,
        },
        {
          id: "683728538cd0aff33c968ecb",
          label: "1 Year",
          valueInDays: 365,
          slug: "1-year",
          features: [
            {
              id: "683725788cd0aff33c968e80",
              name: "Diet Consultation",
              description:
                "Personalized diet consulting tailored to user goals.",
              slug: "diet-consultation",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725838cd0aff33c968e84",
              name: "Workout Consultation",
              description:
                "One-on-one workout consultation, goal-specific planning.",
              slug: "workout-consultation",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683726558cd0aff33c968eb4",
              name: "45-min Video Call Training",
              description: "Personalized video training session with a coach.",
              slug: "45-min-video-call-training",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683725fd8cd0aff33c968ea0",
              name: "Chat Support",
              description:
                "Ongoing chat support for quick queries and guidance.",
              slug: "chat-support",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683726138cd0aff33c968ea4",
              name: "Custom Education Request",
              description:
                "On-demand education modules on topics of your choice.",
              slug: "custom-education-request",
              isActive: true,
              customSort: 1,
            },
            {
              id: "683726228cd0aff33c968ea8",
              name: "Basics of Diet",
              description:
                "Introduction to nutrition principles and macronutrients.",
              slug: "basics-of-diet",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683726338cd0aff33c968eac",
              name: "Basics of Muscles & Fitness Hacks",
              description:
                "Learn muscle groups, function, and performance tips.",
              slug: "basics-of-muscles-&-fitness-hacks",
              isActive: true,
              customSort: 0,
            },
            {
              id: "683726488cd0aff33c968eb0",
              name: "Calorie Tracker Subscription",
              description:
                "In-app calorie tracking support with coaching insights.",
              slug: "calorie-tracker-subscription",
              isActive: true,
              customSort: 0,
            },
          ],
          price: 150000,
          currency: "INR",
          paymentType: "one-time",
          isActive: true,
        },
      ],
      isActive: true,
      isPopular: false,
    },
  ],
};

/**
 * Process and normalize pricing plans data for UI consumption
 * @returns {Array} Processed pricing plans data
 */
export function getPricingPlans() {
  try {
    if (
      !MOCK_PRICING_DATA ||
      !MOCK_PRICING_DATA.data ||
      !Array.isArray(MOCK_PRICING_DATA.data)
    ) {
      throw new Error("Invalid pricing plans data structure");
    }

    return MOCK_PRICING_DATA.data;
  } catch (error) {
    console.error("Error processing pricing plans:", error);
    return [];
  }
}

/**
 * Get all available durations across all plans
 * @returns {Array} Array of duration objects with label and valueInDays
 */
export function getAvailableDurations() {
  const plans = getPricingPlans();
  const durationsMap = new Map();

  plans.forEach((plan) => {
    if (plan.durations && Array.isArray(plan.durations)) {
      plan.durations.forEach((duration) => {
        if (!durationsMap.has(duration.slug)) {
          durationsMap.set(duration.slug, {
            id: duration.id,
            label: duration.label,
            valueInDays: duration.valueInDays,
            slug: duration.slug,
          });
        }
      });
    }
  });

  // Sort by valueInDays
  return Array.from(durationsMap.values()).sort(
    (a, b) => a.valueInDays - b.valueInDays
  );
}

/**
 * Get plan data for a specific duration
 * @param {string} durationSlug - The duration slug (e.g., '1-month', '3-months')
 * @returns {Array} Array of plans with pricing and features for the specified duration
 */
export function getPlansByDuration(durationSlug) {
  const plans = getPricingPlans();

  return plans
    .map((plan) => {
      const duration = plan.durations?.find((d) => d.slug === durationSlug);

      if (!duration) {
        return null;
      }

      return {
        id: plan.id,
        name: plan.name,
        slug: plan.slug,
        description: plan.description,
        isPopular: plan.isPopular || false,
        duration: {
          id: duration.id,
          label: duration.label,
          valueInDays: duration.valueInDays,
          slug: duration.slug,
        },
        price: duration.price,
        currency: duration.currency || "INR",
        paymentType: duration.paymentType || "one-time",
        features: duration.features || [],
      };
    })
    .filter(Boolean); // Remove null entries
}

/**
 * Format price for display
 * @param {number} price - The price amount
 * @param {string} currency - The currency code (default: 'INR')
 * @returns {string} Formatted price string
 */
export function formatPrice(price, currency = "INR") {
  if (typeof price !== "number") {
    return "Price not available";
  }

  const currencySymbols = {
    INR: "₹",
    USD: "$",
    EUR: "€",
    GBP: "£",
  };

  const symbol = currencySymbols[currency] || currency;

  // Format with Indian number system for INR
  if (currency === "INR") {
    return `${symbol} ${price.toLocaleString("en-IN")}`;
  }

  return `${symbol} ${price.toLocaleString()}`;
}

/**
 * Get the most popular plan for a given duration
 * @param {string} durationSlug - The duration slug
 * @returns {Object|null} The most popular plan or null if none found
 */
export function getPopularPlan(durationSlug) {
  const plans = getPlansByDuration(durationSlug);
  return plans.find((plan) => plan.isPopular) || null;
}

/**
 * Sort plans by price (ascending)
 * @param {Array} plans - Array of plan objects
 * @returns {Array} Sorted plans array
 */
export function sortPlansByPrice(plans) {
  return [...plans].sort((a, b) => a.price - b.price);
}

/**
 * Get plan features formatted for display
 * @param {Array} features - Array of feature objects
 * @returns {Array} Formatted features array
 */
export function formatPlanFeatures(features) {
  if (!Array.isArray(features)) {
    return [];
  }

  return features
    .filter((feature) => feature.isActive)
    .sort((a, b) => (a.customSort || 0) - (b.customSort || 0))
    .map((feature) => ({
      id: feature.id,
      name: feature.name,
      description: feature.description,
      slug: feature.slug,
    }));
}

/**
 * Calculate savings percentage compared to monthly pricing
 * @param {number} currentPrice - Current plan price
 * @param {number} monthlyPrice - Monthly plan price
 * @param {number} durationInMonths - Duration in months
 * @returns {number} Savings percentage (0-100)
 */
export function calculateSavings(currentPrice, monthlyPrice, durationInMonths) {
  if (!monthlyPrice || !durationInMonths || durationInMonths <= 1) {
    return 0;
  }

  const totalMonthlyPrice = monthlyPrice * durationInMonths;
  const savings = totalMonthlyPrice - currentPrice;
  const savingsPercentage = (savings / totalMonthlyPrice) * 100;

  return Math.max(0, Math.round(savingsPercentage));
}
