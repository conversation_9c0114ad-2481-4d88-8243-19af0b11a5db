"use client";

import { useEffect } from "react";
import { useState, useRef } from "react";
import Image from "next/image";

import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import Link from "next/link";

const pricingData = {
  "1 Month": {
    "Diet Plan": 1500,
    "Workout Plan": 2000,
    "Diet + Workout Plan": 3500,
    "Premium Plan (12 VCs/m)": 15000,
  },
  "3 Months": {
    "Diet Plan": 4500,
    "Workout Plan": 5500,
    "Diet + Workout Plan": 10000,
    "Premium Plan (12 VCs/m)": 42000,
  },
  "6 Months": {
    "Diet Plan": 9000,
    "Workout Plan": 9000,
    "Diet + Workout Plan": 18000,
    "Premium Plan (12 VCs/m)": 80000,
  },
  "1 Year": {
    "Diet Plan": 15000,
    "Workout Plan": 15000,
    "Diet + Workout Plan": 30000,
    "Premium Plan (12 VCs/m)": 150000,
  },
};

const planNotes = {
  "Diet Plan":
    "Diet charts will be updated every 15 days. As per preference: Vegan/Eggitarian/Veg./Non-veg",
  "Workout Plan":
    "Workout charts will be updated every 15 days. As per preference: Home/Gym or using Resistance bands",
  "Diet + Workout Plan": "Workout & Diet charts will be updated every 15 days.",
  "Premium Plan (12 VCs/m)":
    "45 min of VC, 3 times/week Dedicated coach + Goodies",
};

gsap.registerPlugin(ScrollTrigger);

export default function PricingPlans() {
  const heroRef = useRef(null);
  const sectionRefs = useRef([]);
  sectionRefs.current = [];

  const addToRefs = (el) => {
    if (el && !sectionRefs.current.includes(el)) {
      sectionRefs.current.push(el);
    }
  };

  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;

    requestAnimationFrame(() => {
      if (heroRef.current) {
        gsap.fromTo(
          heroRef.current,
          { opacity: 0, y: 50 },
          { opacity: 1, y: 0, duration: 1.2, ease: "power3.out" }
        );
      }

      sectionRefs.current.forEach((section) => {
        if (!section) return;
        gsap.fromTo(
          section,
          { opacity: 0, y: 50 },
          {
            opacity: 1,
            y: 0,
            duration: 1,
            ease: "power3.out",
            scrollTrigger: {
              trigger: section,
              start: "top 80%",
              toggleActions: "play none none reverse",
              markers: false, // useful for debugging
            },
          }
        );
      });
    });
  }, [isClient]);

  const durations = Object.keys(pricingData);
  const [selectedDuration, setSelectedDuration] = useState("1 Month");

  return (
    <div className="bg-primary theme-secondary-font">
      <section
        className="bg-teal-100 py-16 pt-20 text-center bg-cover bg-center flex items-center justify-center overflow-x-hidden"
        style={{ backgroundImage: "url(/images/bg.png)" }}
      >
        <div ref={heroRef}>
          <h2 className="text-3xl font-extrabold text-center text-teal-600 mb-6 uppercase">
            Plans & Pricing
          </h2>

          {/* Tabs */}
          <div className="flex justify-center space-x-6 border-b pb-3 mb-8 px-4 sm:px-0">
            {durations.map((duration) => (
              <button
                key={duration}
                onClick={() => setSelectedDuration(duration)}
                className={`sm:text-lg font-semibold uppercase ${
                  selectedDuration === duration
                    ? "text-teal-600 border-b-4 border-teal-600"
                    : "text-gray-700"
                }`}
              >
                {duration}
              </button>
            ))}
          </div>

          {/* Plan Cards */}
          <div className="space-y-6 px-10">
            {Object.entries(pricingData[selectedDuration]).map(
              ([planName, price], index) => (
                <div
                  key={planName}
                  className={`flex justify-between items-center bg-white rounded-xl shadow px-6 py-5 ${
                    index === 0 ? "border-2 border-blue-500" : ""
                  }`}
                >
                  <div>
                    <h3 className="text-xl font-bold text-teal-600 text-start">
                      {planName}
                    </h3>
                    <p className="text-gray-600 text-sm max-w-md mt-1 text-start">
                      {planNotes[planName]}
                    </p>
                  </div>
                  <div className="text-xl font-bold text-gray-800 whitespace-nowrap">
                    ₹ {price.toLocaleString()}
                  </div>
                </div>
              )
            )}
          </div>

          {/* Register Button */}
          <div className="flex justify-center mt-10">
            <Link
                href="/#register"
                className="bg-teal-600 hover:bg-red-700 text-white text-lg font-bold py-3 px-10 rounded-full"
              >
                REGISTER NOW
              </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
