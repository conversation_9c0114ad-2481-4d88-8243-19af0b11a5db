"use client";

import { useEffect, useState, useRef } from "react";
import { motion } from "framer-motion";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Import our new components and utilities
import PlanCard from "../../components/Pricing/PlanCard";
import Button from "../../components/UI/Button";
import Tag from "../../components/UI/Tag";
import {
  getAvailableDurations,
  getPlansByDuration,
  getPopularPlan,
} from "../../data/pricing-plans";

gsap.registerPlugin(ScrollTrigger);

export default function PricingPlans() {
  const heroRef = useRef(null);
  const sectionRefs = useRef([]);
  sectionRefs.current = [];

  const addToRefs = (el) => {
    if (el && !sectionRefs.current.includes(el)) {
      sectionRefs.current.push(el);
    }
  };

  const [isClient, setIsClient] = useState(false);
  const [availableDurations, setAvailableDurations] = useState([]);
  const [selectedDuration, setSelectedDuration] = useState("");
  const [currentPlans, setCurrentPlans] = useState([]);
  const [loading, setLoading] = useState(true);

  // Initialize data
  useEffect(() => {
    setIsClient(true);

    try {
      const durations = getAvailableDurations();
      setAvailableDurations(durations);

      // Set default duration (1 month if available, otherwise first duration)
      const defaultDuration =
        durations.find((d) => d.valueInDays === 30) || durations[0];
      if (defaultDuration) {
        setSelectedDuration(defaultDuration.slug);
      }

      setLoading(false);
    } catch (error) {
      console.error("Error loading pricing data:", error);
      setLoading(false);
    }
  }, []);

  // Update plans when duration changes
  useEffect(() => {
    if (selectedDuration) {
      const plans = getPlansByDuration(selectedDuration);
      setCurrentPlans(plans);
    }
  }, [selectedDuration]);

  // GSAP animations
  useEffect(() => {
    if (!isClient) return;

    requestAnimationFrame(() => {
      if (heroRef.current) {
        gsap.fromTo(
          heroRef.current,
          { opacity: 0, y: 50 },
          { opacity: 1, y: 0, duration: 1.2, ease: "power3.out" }
        );
      }

      sectionRefs.current.forEach((section) => {
        if (!section) return;
        gsap.fromTo(
          section,
          { opacity: 0, y: 50 },
          {
            opacity: 1,
            y: 0,
            duration: 1,
            ease: "power3.out",
            scrollTrigger: {
              trigger: section,
              start: "top 80%",
              toggleActions: "play none none reverse",
              markers: false,
            },
          }
        );
      });
    });
  }, [isClient]);

  // Handle duration change for individual plan cards
  const handleDurationChange = (newDuration) => {
    setSelectedDuration(newDuration);
  };

  // Handle Buy Now button click (static for now)
  const handleBuyNow = (plan) => {
    console.log("Buy Now clicked for plan:", plan);
    // TODO: Implement Razorpay integration in Phase 2
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading pricing plans...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 theme-secondary-font min-h-screen">
      {/* Hero Section */}
      <section
        className="bg-gradient-to-br from-teal-50 to-blue-50 py-12 sm:py-16 pt-16 sm:pt-20 overflow-x-hidden"
        style={{ backgroundImage: "url(/images/bg.png)" }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            ref={heroRef}
            className="text-center"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-extrabold text-gray-900 mb-4 px-4">
              Choose Your <span className="text-teal-600">Fitness Journey</span>
            </h1>
            <p className="text-lg sm:text-xl text-gray-600 mb-6 sm:mb-8 max-w-3xl mx-auto px-4">
              Transform your health with our personalized fitness and nutrition
              plans. Choose the perfect plan that fits your goals and lifestyle.
            </p>

            {/* Duration Tabs */}
            <div className="flex flex-wrap justify-center gap-2 mb-8 sm:mb-12 px-4">
              {availableDurations.map((duration) => (
                <motion.button
                  key={duration.slug}
                  onClick={() => setSelectedDuration(duration.slug)}
                  className={`px-4 sm:px-6 py-2 sm:py-3 rounded-full font-semibold transition-all duration-200 text-sm sm:text-base ${
                    selectedDuration === duration.slug
                      ? "bg-teal-600 text-white shadow-lg"
                      : "bg-white text-gray-700 hover:bg-gray-100 shadow"
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {duration.label}
                </motion.button>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Pricing Plans Section */}
      <section className="py-8 sm:py-12 lg:py-16 -mt-4 sm:-mt-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {currentPlans.length > 0 ? (
            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, staggerChildren: 0.1 }}
            >
              {currentPlans.map((plan, index) => {
                const popularPlan = getPopularPlan(selectedDuration);
                const isPopular = popularPlan && plan.id === popularPlan.id;

                return (
                  <motion.div
                    key={`${plan.id}-${selectedDuration}`}
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex"
                  >
                    <PlanCard
                      plan={plan}
                      availableDurations={availableDurations}
                      selectedDuration={selectedDuration}
                      onDurationChange={handleDurationChange}
                      isPopular={isPopular}
                      onBuyNow={handleBuyNow}
                      className="w-full"
                    />
                  </motion.div>
                );
              })}
            </motion.div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500 text-base sm:text-lg">
                No plans available for the selected duration.
              </p>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-teal-600 py-16">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              Ready to Start Your Transformation?
            </h2>
            <p className="text-teal-100 text-lg mb-8">
              Join thousands of satisfied customers who have achieved their
              fitness goals with our expert guidance.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="secondary"
                size="lg"
                className="bg-white text-teal-600 hover:bg-gray-100"
              >
                View Sample Plans
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-white text-white hover:bg-white hover:text-teal-600"
              >
                Contact Support
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
